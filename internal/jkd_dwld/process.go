package jkd_dwld

import (
	"QuestionsDownloader/pkg/utils"
	"QuestionsDownloader/types"
	"fmt"
	"log"
	"path"
	"sync"
)

func ProcessSubjects(
	downloadDir string,
	subjectConfigs []types.SubjectConfig,
) []types.SubjectConfig {

	var exportSubjects []types.SubjectConfig
	var exportMutex sync.Mutex

	// 处理函数
	processFunc := func(downloadDir string, subject types.SubjectConfig) error {
		export, err := processSubject(downloadDir, subject)
		if err != nil {
			return err
		}

		exportMutex.Lock()
		exportSubjects = append(exportSubjects, export)
		exportMutex.Unlock()
		return nil
	}

	var err error
	mode := utils.MustGetEnv("JKD_DOWNLOAD_PROCESS_MODE")
	switch mode {
	case "serial":
		err = processSerially(downloadDir, subjectConfigs, processFunc)
	case "concurrent":
		err = processConcurrently(downloadDir, subjectConfigs, processFunc)
	default:
		log.Printf("未知的执行模式: %s", mode)

	}

	if err != nil {
		log.Printf("处理下载所有科目出现错误: %v", err)
		return nil
	}

	return exportSubjects
}

// processes subjects in serial mode
func processSerially(
	downloadDir string,
	subjectConfigs []types.SubjectConfig,
	processFunc func(downloadDir string, subject types.SubjectConfig) error,
) error {
	log.Println("使用串行模式处理下载任务...")

	for _, subject := range subjectConfigs {
		if err := processFunc(downloadDir, subject); err != nil {
			return fmt.Errorf("串行处理科目 %s 时出错: %v", subject.JKDSubjectName, err)
		}
	}

	return nil
}

// processes subjects in concurrent mode
func processConcurrently(
	downloadDir string,
	subjectConfigs []types.SubjectConfig,
	processFunc func(downloadDir string, subject types.SubjectConfig) error,
) error {
	maxConcurrent := utils.MustGetEnvAsInt("JKD_DOWNLOAD_PROCESS_MAX_CONCURRENT")
	log.Printf("使用并发模式处理下载任务（最大并发数：%d）...\n", maxConcurrent)

	// 创建信号量来控制并发数
	semaphore := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup
	errors := make(chan error, len(subjectConfigs))

	for _, subject := range subjectConfigs {
		wg.Add(1)
		semaphore <- struct{}{} // 获取信号量

		go func(subject types.SubjectConfig) {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			if err := processFunc(downloadDir, subject); err != nil {
				errors <- fmt.Errorf("并发处理科目 %s 时出错: %v", subject.JKDSubjectName, err)
			}
		}(subject)
	}

	// 等待所有任务完成
	wg.Wait()
	close(errors)

	// 检查是否有错误发生
	for err := range errors {
		if err != nil {
			return err
		}
	}

	return nil
}

// processes a single subject download and returns export info
func processSubject(
	downloadDir string,
	subject types.SubjectConfig,
) (types.SubjectConfig, error) {
	// 获取更新名称
	userId := utils.MustGetEnv("JKD_AUTH_USER_ID")
	password := utils.MustGetEnv("JKD_AUTH_PASSWORD")
	updateName, err := GetSubjectUpdateName(subject.JKDSubjectId, userId, password)
	if err != nil {
		return types.SubjectConfig{}, fmt.Errorf("获取更新名称失败: %v", err)
	}

	// 如果获取到的 updateName 为空，尝试使用已知的映射
	if updateName == "" {
		log.Printf("SOAP返回空的updateName，尝试使用已知映射...")
		knownUpdateNames := map[string]string{
			"1":  "1_1_2412111041520651",   // 一建建设工程项目管理
			"3":  "3_1_2412111042346045",   // 一建建设工程法规及相关知识
			"19": "19_1_2411261745195631",  // 一建建设工程经济
			"9":  "9_1_2412111030007888",   // 一建建筑工程实务
			"10": "10_1_2412111026516175",  // 一建机电工程实务
			"12": "12_1_2412051703413357",  // 一建水利水电工程实务
			"11": "11_1_2412111021362255",  // 一建市政公用工程实务
			"17": "17_1_2411261529218378",  // 一建矿业工程实务
			"13": "13_1_2412111019062678",  // 一建公路工程实务
		}

		if knownName, exists := knownUpdateNames[subject.JKDSubjectId]; exists {
			updateName = knownName
			log.Printf("使用已知的updateName: %s", updateName)
		} else {
			return types.SubjectConfig{}, fmt.Errorf("无法获取updateName，且没有已知映射 for subjectId: %s", subject.JKDSubjectId)
		}
	}

	log.Printf("当前科目 %s 获取更新名称成功: %s", subject, updateName)

	// 创建下载目录
	downloadPath := path.Join(downloadDir, subject.SubDir)
	if err := utils.CreateDirectory(downloadPath); err != nil {
		return types.SubjectConfig{}, fmt.Errorf("创建下载目录失败: %v", err)
	}

	// 下载文件
	if err := DownloadSubjectFile(updateName, downloadPath); err != nil {
		return types.SubjectConfig{}, fmt.Errorf("下载文件失败: %v", err)
	}

	log.Printf("当前科目 %s %s 处理完成，文件已下载", subject, updateName)

	// 返回导出信息
	return types.SubjectConfig{
		JKDCertificateId:   subject.JKDCertificateId,
		JKDCertificateName: subject.JKDCertificateName,
		JKDSubjectId:       subject.JKDSubjectId,
		JKDSubjectName:     subject.JKDSubjectName,
		JKDUpdateName:      updateName,
		SubDir:             subject.SubDir,
		LexCertificate:     subject.LexCertificate,
		LexSubject:         subject.LexSubject,
		CertificateCode:    subject.CertificateCode,
		SubjectCode:        subject.SubjectCode,
	}, nil
}
