package jkd_dwld

import (
	"encoding/xml"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// SOAPEnvelope represents the XML structure for SOAP responses
type SOAPEnvelope struct {
	XMLName xml.Name `xml:"Envelope"`
	Body    struct {
		GetCourseUpdateNameResponse struct {
			Result string `xml:"GetCourseUpdateNameResult"`
		} `xml:"GetCourseUpdateNameResponse"`
	} `xml:"Body"`
}

const (
	soapURL          = "http://iphone.jinkaodian.com/ExamJsonService.asmx"
	downloadURL      = "http://www.jinkaodian.com/CL.ExamWebService/subject/%s.zip"
	soapBodyTemplate = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Header>
        <MGSoapHeader xmlns="http://tempuri.org/">
            <UserID>%s</UserID>
            <Password>%s</Password>
        </MGSoapHeader>
    </soap:Header>
    <soap:Body>
        <GetCourseUpdateName xmlns="http://tempuri.org/">
            <courseId>%s</courseId>
            <district></district>
            <courseType>1</courseType>
        </GetCourseUpdateName>
    </soap:Body>
</soap:Envelope>`
)

// GetSubjectUpdateName retrieves update name for a subject via SOAP request
func GetSubjectUpdateName(subjectId string, userId string, password string) (string, error) {
	// 准备 SOAP 请求体
	body := fmt.Sprintf(soapBodyTemplate, userId, password, subjectId)
	log.Printf("SOAP请求参数 - SubjectId: %s, UserId: %s, Password: %s", subjectId, userId, password)
	log.Printf("SOAP请求体: %s", body)
	req, err := http.NewRequest("POST", soapURL, strings.NewReader(body))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("Host", "iphone.jinkaodian.com")
	req.Header.Set("SOAPAction", "http://tempuri.org/GetCourseUpdateName")
	req.Header.Set("Content-Type", "text/xml; charset=utf-8")
	req.Header.Set("User-Agent", "éèå¸ 3.15 (iPad; iPadOS 17.5; en_CN)")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			fmt.Printf("关闭响应体失败: %v\n", err)
		}
	}(resp.Body)

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 添加调试信息
	log.Printf("SOAP响应状态码: %d", resp.StatusCode)
	log.Printf("SOAP响应内容: %s", string(respBody))

	// 解析 XML 响应
	var envelope SOAPEnvelope
	if err := xml.Unmarshal(respBody, &envelope); err != nil {
		log.Printf("XML解析失败: %v", err)
		return "", err
	}

	result := envelope.Body.GetCourseUpdateNameResponse.Result
	log.Printf("解析得到的updateName: '%s'", result)
	return result, nil
}

// DownloadSubjectFile 下载指定文件到目标路径
func DownloadSubjectFile(updateName, downloadPath string) error {
	// 尝试多个可能的URL格式
	possibleURLs := []string{
		fmt.Sprintf("http://www.jinkaodian.com/CL.ExamWebService/subject/%s.zip", updateName),
		fmt.Sprintf("http://iphone.jinkaodian.com/CL.ExamWebService/subject/%s.zip", updateName),
		fmt.Sprintf("http://www.jinkaodian.com/ExamWebService/subject/%s.zip", updateName),
		fmt.Sprintf("http://iphone.jinkaodian.com/ExamWebService/subject/%s.zip", updateName),
		fmt.Sprintf("http://www.jinkaodian.com/subject/%s.zip", updateName),
		fmt.Sprintf("http://iphone.jinkaodian.com/subject/%s.zip", updateName),
	}

	var lastErr error
	for i, url := range possibleURLs {
		log.Printf("尝试下载URL %d/%d: %s", i+1, len(possibleURLs), url)

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			lastErr = err
			continue
		}

		// 设置请求头
		req.Header.Set("Host", "www.jinkaodian.com")
		req.Header.Set("Accept", "*/*")
		req.Header.Set("User-Agent", "jkdexam/3.15 CFNetwork/1496.0.7 Darwin/23.5.0")
		req.Header.Set("Accept-Language", "zh-CN,zh-Hans;q=0.9")
		req.Header.Set("Cache-Control", "no-cache")

		// 发送请求
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			lastErr = err
			log.Printf("请求失败: %v", err)
			continue
		}

		log.Printf("响应状态码: %d", resp.StatusCode)

		if resp.StatusCode == http.StatusOK {
			// 成功找到文件，继续下载
			defer func(Body io.ReadCloser) {
				err := Body.Close()
				if err != nil {
					log.Printf("关闭响应体失败: %v\n", err)
				}
			}(resp.Body)

			// 创建文件
			filename := filepath.Join(downloadPath, updateName+".zip")
			file, err := os.Create(filename)
			if err != nil {
				return err
			}
			defer func(file *os.File) {
				err := file.Close()
				if err != nil {
					log.Printf("关闭文件失败: %v\n", err)
				}
			}(file)

			// 写入文件
			_, err = io.Copy(file, resp.Body)
			if err != nil {
				return err
			}

			log.Printf("文件下载成功: %s", filename)
			return nil
		} else {
			resp.Body.Close()
			lastErr = fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
		}
	}

	return fmt.Errorf("所有URL都失败了，最后一个错误: %v", lastErr)
	if err != nil {
		return err
	}

	// 设置请求头
	req.Header.Set("Host", "www.jinkaodian.com")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("User-Agent", "jkdexam/3.15 CFNetwork/1496.0.7 Darwin/23.5.0")
	req.Header.Set("Accept-Language", "zh-CN,zh-Hans;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Printf("关闭响应体失败: %v\n", err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建文件
	filename := filepath.Join(downloadPath, updateName+".zip")
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			log.Printf("关闭文件失败: %v\n", err)
		}
	}(file)

	// 写入文件
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return err
	}

	return nil
}
